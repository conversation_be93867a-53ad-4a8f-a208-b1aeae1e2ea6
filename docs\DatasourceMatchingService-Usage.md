# 数据源匹配服务使用指南 (Datasource Matching Service Usage Guide)

## 概述 (Overview)

`DatasourceMatchingService` 是一个用于匹配 `lineage_datasources` 和 `metadata_data_source` 表的服务。该服务采用数据导向编程方法，在内存中执行匹配操作，提供高效且可测试的数据源匹配功能。

## 核心特性 (Core Features)

1. **内存匹配 (In-Memory Matching)**: 从数据库查询所有数据后在内存中进行匹配，避免复杂的 SQL 查询
2. **函数式核心 (Functional Core)**: 匹配逻辑封装在纯函数中，易于测试和维护
3. **不可变数据结构 (Immutable Data Structures)**: 使用 Kotlin 数据类确保数据不可变性
4. **详细统计信息 (Detailed Statistics)**: 提供匹配过程的完整统计数据
5. **Spring Data JDBC 集成**: 遵循项目的持久化层最佳实践

## 数据模型 (Data Models)

### 血缘数据源实体 (Lineage Datasource Entity)
```kotlin
@Table("lineage_datasources")
data class LineageDatasource(
    @Id val id: Long = 0,
    @Column("datasource_name") val datasourceName: String,
    @Column("db_type") val dbType: String,
    @Column("host") val host: String,
    @Column("port") val port: Int,
    @Column("database_name") val databaseName: String,
    // ... 其他字段
)
```

### 元数据数据源实体 (Metadata Data Source Entity)
```kotlin
@Table("metadata_data_source")
data class MetadataDataSource(
    @Id val id: Long = 0,
    @Column("SOURCE_NAME") val sourceName: String,
    @Column("DB_TYPE") val dbType: String,
    @Column("DB_URL") val dbUrl: String?,
    @Column("DB_PORT") val dbPort: Int?,
    @Column("DB_NAME") val dbName: String,
    // ... 其他字段
)
```

### 匹配结果 (Matching Result)
```kotlin
data class DatasourceMatchingResult(
    val matchedPairs: List<MatchedDatasourcePair>,
    val unmatchedLineageDatasources: List<LineageDatasource>,
    val unmatchedMetadataDataSources: List<MetadataDataSource>,
    val statistics: MatchingStatistics
)
```

## 匹配条件 (Matching Criteria)

数据源匹配基于以下条件：

1. **数据库类型匹配**: `LOWER(lineage_datasources.db_type) = LOWER(metadata_data_source.DB_TYPE)`
2. **主机匹配**: `lineage_datasources.host = metadata_data_source.DB_URL`
3. **端口匹配**: `lineage_datasources.port = COALESCE(metadata_data_source.DB_PORT, 3306)`
4. **数据库名称匹配**: `lineage_datasources.database_name = metadata_data_source.DB_NAME`

## 使用方法 (Usage)

### 基本匹配操作

```kotlin
@Service
class YourService(
    private val datasourceMatchingService: DatasourceMatchingService
) {
    
    fun performDatasourceMatching() {
        // 执行完整的数据源匹配
        val result = datasourceMatchingService.matchDatasources()
        
        // 处理匹配结果
        println("匹配成功的数据源对: ${result.matchedPairs.size}")
        println("未匹配的血缘数据源: ${result.unmatchedLineageDatasources.size}")
        println("未匹配的元数据数据源: ${result.unmatchedMetadataDataSources.size}")
        
        // 查看统计信息
        val stats = result.statistics
        println("匹配率: ${String.format("%.2f", stats.matchingRate * 100)}%")
        println("处理时间: ${stats.processingTimeMs}ms")
    }
}
```

### 查找特定匹配

```kotlin
// 根据血缘数据源ID查找匹配的元数据数据源
val metadataDataSource = datasourceMatchingService
    .findMatchedMetadataDataSourceByLineageId(lineageDatasourceId)

if (metadataDataSource != null) {
    println("找到匹配的元数据数据源: ${metadataDataSource.sourceName}")
} else {
    println("未找到匹配的元数据数据源")
}

// 根据元数据数据源ID查找匹配的血缘数据源
val lineageDatasource = datasourceMatchingService
    .findMatchedLineageDatasourceByMetadataId(metadataDataSourceId)
```

### 检查匹配状态

```kotlin
// 检查特定血缘数据源是否有匹配
val hasMatch = datasourceMatchingService.hasMatchedMetadataDataSource(lineageDatasourceId)

if (hasMatch) {
    println("血缘数据源 $lineageDatasourceId 有匹配的元数据数据源")
}

// 检查特定元数据数据源是否有匹配
val hasLineageMatch = datasourceMatchingService.hasMatchedLineageDatasource(metadataDataSourceId)
```

### 获取统计信息

```kotlin
// 仅获取统计信息，不处理具体匹配结果
val statistics = datasourceMatchingService.getMatchingStatistics()

println("总血缘数据源数: ${statistics.totalLineageDatasources}")
println("总元数据数据源数: ${statistics.totalMetadataDataSources}")
println("匹配成功数: ${statistics.matchedPairsCount}")
println("匹配率: ${String.format("%.2f", statistics.matchingRate * 100)}%")
```

## 测试示例 (Testing Examples)

### 单元测试

```kotlin
@ExtendWith(MockitoExtension::class)
class DatasourceMatchingServiceTest {
    
    @Mock
    private lateinit var jdbcTemplate: JdbcTemplate
    
    @InjectMocks
    private lateinit var datasourceMatchingService: DatasourceMatchingService
    
    @Test
    fun `should match datasources correctly`() {
        // 准备测试数据
        val lineageDatasources = listOf(/* 测试数据 */)
        val metadataDataSources = listOf(/* 测试数据 */)
        
        // Mock JDBC 查询
        whenever(jdbcTemplate.query(any<String>(), any<RowMapper<LineageDatasource>>()))
            .thenReturn(lineageDatasources)
        whenever(jdbcTemplate.query(any<String>(), any<RowMapper<MetadataDataSource>>()))
            .thenReturn(metadataDataSources)
        
        // 执行测试
        val result = datasourceMatchingService.matchDatasources()
        
        // 验证结果
        assertEquals(expectedMatchCount, result.matchedPairs.size)
    }
}
```

## 性能考虑 (Performance Considerations)

1. **内存使用**: 服务会将所有数据源加载到内存中，适用于中等规模的数据集
2. **缓存策略**: 考虑在高频调用场景下实现结果缓存
3. **批处理**: 对于大量数据源，可以考虑分批处理

## 日志记录 (Logging)

服务提供详细的日志记录：

- **INFO 级别**: 匹配过程的关键步骤和统计信息
- **DEBUG 级别**: 详细的匹配过程信息
- **WARN 级别**: 未匹配的数据源信息

每个日志消息都有唯一的标识符，便于追踪和调试。

## 扩展建议 (Extension Suggestions)

1. **自定义匹配规则**: 可以扩展服务以支持自定义匹配条件
2. **结果缓存**: 实现匹配结果的缓存机制
3. **异步处理**: 对于大数据集，可以实现异步匹配
4. **匹配质量评分**: 为匹配结果添加质量评分机制

## 注意事项 (Important Notes)

1. 服务假设数据库连接是可用的
2. 匹配是基于精确字符串比较（除了数据库类型的大小写不敏感）
3. 默认端口设置为 3306（MySQL 默认端口）
4. 只处理状态为 'ACTIVE' 的血缘数据源和 ACTIVE_FLAG = 1 的元数据数据源
