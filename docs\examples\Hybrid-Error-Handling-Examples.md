# Hybrid Error Handling Examples

## Real-world Migration Examples from Codebase

### Example 1: Script Analysis Service Migration

**Before: Mixed Exception and Result Handling**
```kotlin
// From ScriptAnalysisService.kt
fun analyzeScript(script: Script): ScriptLineageResult {
    return try {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()
        
        // 1. 解析SQL
        val parseResult = sqlParser.parseScript(script.content)
        if (parseResult.errors.isNotEmpty()) {
            errors.addAll(parseResult.errors)
            return ScriptLineageResult(null, warnings, errors, false)
        }
        
        // 2. 构建血缘
        val lineage = buildLineage(parseResult.statements)
        ScriptLineageResult(lineage, warnings, errors, true)
        
    } catch (e: Exception) {
        ScriptLineageResult(null, emptyList(), listOf(e.message ?: "Unknown error"), false)
    }
}
```

**After: Hybrid Approach with OperationResult**
```kotlin
fun analyzeScript(script: Script): OperationResult<DataLineage> {
    return OperationResult.catchingResult {
        val warnings = mutableListOf<String>()
        
        // 1. 验证脚本 - 预期的业务错误
        if (script.content.isBlank()) {
            return@catchingResult OperationResult.failure("脚本内容不能为空")
        }
        
        if (script.content.length > MAX_SCRIPT_LENGTH) {
            return@catchingResult OperationResult.failure(
                "脚本长度超过限制: ${script.content.length} > $MAX_SCRIPT_LENGTH"
            )
        }
        
        // 2. 解析SQL - 可能部分成功
        val parseResult = parseScriptSafely(script.content, warnings)
        if (parseResult.isEmpty()) {
            return@catchingResult OperationResult.failure(
                "所有SQL语句解析失败",
                warnings = warnings
            )
        }
        
        // 3. 构建血缘 - 系统级错误会抛出异常
        val lineage = buildLineage(parseResult) // 可能抛出 DatabaseException
        
        OperationResult.success(lineage, warnings = warnings)
    }
}

private fun parseScriptSafely(content: String, warnings: MutableList<String>): List<ParsedStatement> {
    val statements = content.split(";")
    val parsed = mutableListOf<ParsedStatement>()
    
    statements.forEach { statement ->
        try {
            parsed.add(sqlParser.parseStatement(statement))
        } catch (e: SqlParsingException) {
            // 解析错误是预期的，记录警告继续处理
            warnings.add("无法解析SQL语句: ${statement.take(50)}... - ${e.message}")
        }
    }
    
    return parsed
}
```

### Example 2: Task Processing Service

**Before: Exception Swallowing**
```kotlin
// From LineageTaskService.kt
fun processJobLineage(job: DataExchangeJob): TaskProcessResult {
    return try {
        val startTime = System.currentTimeMillis()
        val lineageResult = dataExchangeJobService.processJobLineage(job)
        
        TaskProcessResult(
            taskId = 0,
            jobKey = "${job.readerJobId}_${job.writeJobId}",
            taskName = "数据同步任务",
            status = if (lineageResult.success) TaskStatus.COMPLETED else TaskStatus.FAILED,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = lineageResult.success,
            processingResult = if (lineageResult.success) ProcessingResult.UPDATED else ProcessingResult.FAILED,
            errorMessage = if (!lineageResult.success) lineageResult.errors.joinToString("; ") else null
        )
    } catch (e: Exception) {
        logger.error("处理作业血缘时发生异常", e)
        createFailedTaskResult(job, e)
    }
}
```

**After: Proper Error Classification**
```kotlin
fun processJobLineage(job: DataExchangeJob): OperationResult<TaskProcessResult> {
    val startTime = System.currentTimeMillis()
    
    return OperationResult.catchingResult {
        // 1. 验证作业参数 - 业务错误
        validateJobParameters(job).let { validation ->
            if (!validation.success) {
                return@catchingResult validation.map { null as TaskProcessResult? }
            }
        }
        
        // 2. 处理血缘 - 可能有系统错误
        val lineageResult = OperationResult.catching(
            operation = { dataExchangeJobService.processJobLineage(job) },
            onException = { exception ->
                when (exception) {
                    is DatabaseConnectionException -> throw exception // 系统错误，重新抛出
                    is ConfigurationException -> throw exception // 配置错误，重新抛出
                    is DataProcessingException -> OperationResult.failure(
                        "数据处理失败: ${exception.message}",
                        preserveException = true
                    )
                    else -> OperationResult.fromException(exception)
                }
            }
        )
        
        // 3. 构建任务结果
        val taskResult = TaskProcessResult(
            taskId = 0,
            jobKey = "${job.readerJobId}_${job.writeJobId}",
            taskName = "数据同步任务-${job.readerJobName}到${job.writeJobName}",
            status = if (lineageResult.success) TaskStatus.COMPLETED else TaskStatus.FAILED,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = lineageResult.success && lineageResult.data?.hasChanges == true,
            processingResult = determineProcessingResult(lineageResult),
            errorMessage = if (!lineageResult.success) lineageResult.getAllIssues().joinToString("; ") else null
        )
        
        if (lineageResult.success) {
            OperationResult.success(taskResult, warnings = lineageResult.warnings)
        } else {
            OperationResult.failure(
                "任务处理失败",
                errors = lineageResult.errors,
                warnings = lineageResult.warnings
            )
        }
    }
}

private fun validateJobParameters(job: DataExchangeJob): OperationResult<Unit> {
    val errors = mutableListOf<String>()
    
    if (job.readerJobId.isBlank()) errors.add("读取作业ID不能为空")
    if (job.writeJobId.isBlank()) errors.add("写入作业ID不能为空")
    if (job.readerJobName.isBlank()) errors.add("读取作业名称不能为空")
    
    return if (errors.isEmpty()) {
        OperationResult.success(Unit)
    } else {
        OperationResult.failure("作业参数验证失败", errors)
    }
}
```

### Example 3: Controller Layer Integration

**Before: Inconsistent Error Handling**
```kotlin
@PostMapping("/analyze")
fun analyzeScript(@RequestBody request: ScriptAnalysisRequest): ResponseEntity<ApiResponse<ScriptLineageResult>> {
    return try {
        val result = scriptAnalysisService.analyzeScript(request)
        if (result.success) {
            ResponseEntity.ok(ApiResponse.success(result))
        } else {
            ResponseEntity.badRequest().body(ApiResponse.error("Analysis failed"))
        }
    } catch (e: Exception) {
        ResponseEntity.internalServerError().body(ApiResponse.error("Internal error: ${e.message}"))
    }
}
```

**After: Structured Error Handling**
```kotlin
@PostMapping("/analyze")
fun analyzeScript(@RequestBody request: ScriptAnalysisRequest): ResponseEntity<ApiResponse<DataLineage>> {
    return try {
        val result = scriptAnalysisService.analyzeScript(request.toScript())
        
        when {
            result.isCompleteSuccess() -> {
                logger.info("3f7a9b2d | 脚本分析完全成功: scriptId=${request.scriptId}")
                ResponseEntity.ok(ApiResponse.success(result.data, "分析成功"))
            }
            
            result.isPartialSuccess() -> {
                logger.warn("8e4c1f6a | 脚本分析部分成功: scriptId=${request.scriptId}, warnings=${result.warnings.size}")
                ResponseEntity.ok(ApiResponse.success(
                    result.data, 
                    "分析完成，但有 ${result.warnings.size} 个警告"
                ))
            }
            
            else -> {
                logger.error("2d9b5e8c | 脚本分析失败: scriptId=${request.scriptId}, ${result.getDetailedErrorInfo()}")
                
                // 如果有异常信息，记录用于调试
                result.exception?.let { exception ->
                    logger.debug("5a3f7d1b | 异常详情", exception)
                }
                
                ResponseEntity.badRequest().body(ApiResponse.error(
                    result.message ?: "脚本分析失败"
                ))
            }
        }
    } catch (e: Exception) {
        // 系统级异常由全局异常处理器处理
        logger.error("1c8e4a7f | 脚本分析时发生系统异常: scriptId=${request.scriptId}", e)
        throw e
    }
}
```

### Example 4: Batch Processing with Error Aggregation

```kotlin
fun processBatchLineageTasks(jobs: List<DataExchangeJob>): OperationResult<BatchProcessResult> {
    val results = mutableListOf<OperationResult<TaskProcessResult>>()
    val startTime = System.currentTimeMillis()
    
    // 并行处理所有作业
    jobs.forEach { job ->
        val result = OperationResult.catching(
            operation = { processJobLineage(job) },
            onException = { exception ->
                when (exception) {
                    is SystemException -> throw exception // 系统错误停止整个批处理
                    else -> OperationResult.fromException<TaskProcessResult>(exception)
                }
            }
        )
        results.add(result)
    }
    
    // 聚合结果
    val successfulResults = results.filter { it.success }
    val failedResults = results.filter { !it.success }
    val allWarnings = results.flatMap { it.warnings }
    val allErrors = results.flatMap { it.errors }
    
    val batchResult = BatchProcessResult(
        batchId = generateBatchId(),
        totalJobs = jobs.size,
        processedTasks = results.mapNotNull { it.data },
        summary = ProcessingSummary(
            successful = successfulResults.size,
            failed = failedResults.size,
            unchanged = successfulResults.count { !it.data?.hasChanges ?: false },
            updated = successfulResults.count { it.data?.hasChanges ?: false },
            deactivated = 0
        ),
        processingTimeMs = System.currentTimeMillis() - startTime
    )
    
    return when {
        failedResults.isEmpty() -> OperationResult.success(batchResult, warnings = allWarnings)
        successfulResults.isEmpty() -> OperationResult.failure(
            "批处理完全失败",
            errors = allErrors,
            warnings = allWarnings
        )
        else -> OperationResult.partialSuccess(
            batchResult,
            warnings = allWarnings + "批处理部分失败: ${failedResults.size}/${jobs.size} 个任务失败"
        )
    }
}
```

### Example 5: External API Integration

```kotlin
fun fetchMetadataFromExternalSystem(systemId: Long): OperationResult<MetadataResponse> {
    return OperationResult.catching(
        operation = {
            // 1. 验证系统配置 - 业务错误
            val systemConfig = getSystemConfig(systemId)
                ?: return@catching OperationResult.failure<MetadataResponse>("系统配置未找到: $systemId")
            
            if (!systemConfig.isActive) {
                return@catching OperationResult.failure<MetadataResponse>("系统未激活: $systemId")
            }
            
            // 2. 调用外部API - 可能的网络错误
            val response = try {
                externalApiClient.fetchMetadata(systemConfig.apiUrl, systemConfig.apiKey)
            } catch (e: ConnectTimeoutException) {
                return@catching OperationResult.failure<MetadataResponse>(
                    "连接外部系统超时: ${systemConfig.apiUrl}",
                    preserveException = true
                )
            } catch (e: HttpException) {
                if (e.code() == 404) {
                    return@catching OperationResult.failure<MetadataResponse>("外部系统资源未找到")
                } else {
                    throw e // HTTP 500等服务器错误应该重新抛出
                }
            }
            
            // 3. 验证响应数据
            val warnings = mutableListOf<String>()
            if (response.data.isEmpty()) {
                warnings.add("外部系统返回空数据")
            }
            
            if (response.lastUpdated.isBefore(LocalDateTime.now().minusDays(7))) {
                warnings.add("外部系统数据可能过期: ${response.lastUpdated}")
            }
            
            OperationResult.success(response, warnings = warnings)
        },
        onException = { exception ->
            when (exception) {
                is ConfigurationException -> throw exception
                is SecurityException -> throw exception
                else -> OperationResult.fromException(exception)
            }
        }
    )
}
```

## Key Takeaways

1. **Business Errors**: Use `OperationResult.failure()` for expected validation and business logic errors
2. **System Errors**: Throw exceptions for configuration, security, and infrastructure errors
3. **Partial Success**: Use `OperationResult.partialSuccess()` when some operations succeed with warnings
4. **Exception Preservation**: Use `preserveException = true` for debugging complex failures
5. **Batch Processing**: Aggregate results and provide comprehensive error reporting
6. **Controller Integration**: Handle different success levels appropriately in API responses
7. **Logging Strategy**: Log different error types at appropriate levels with unique trace IDs
