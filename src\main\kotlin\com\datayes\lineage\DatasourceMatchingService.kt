package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import org.springframework.data.repository.CrudRepository
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 血缘数据源实体 (Lineage Datasource Entity)
 *
 * 使用 Spring Data JDBC 映射到 lineage_datasources 表
 */
@Table("lineage_datasources")
data class LineageDatasource(
    @Id
    val id: Long? = null,
    
    @Column("datasource_name")
    val datasourceName: String,
    
    @Column("db_type")
    val dbType: String,
    
    @Column("host")
    val host: String,
    
    @Column("port")
    val port: Int,
    
    @Column("database_name")
    val databaseName: String,
    
    @Column("connection_string")
    val connectionString: String,
    
    @Column("system_id")
    val systemId: Long?,
    
    @Column("status")
    val status: String,
    
    @Column("created_at")
    val createdAt: LocalDateTime?,
    
    @Column("updated_at")
    val updatedAt: LocalDateTime?
)

/**
 * 元数据数据源实体 (Metadata Data Source Entity)
 *
 * 使用 Spring Data JDBC 映射到 metadata_data_source 表
 */
@Table("metadata_data_source")
data class MetadataDataSource(
    @Id
    val id: Long? = null,
    
    @Column("SOURCE_NAME")
    val sourceName: String,
    
    @Column("DB_TYPE")
    val dbType: String,
    
    @Column("DB_DRIVER")
    val dbDriver: String,
    
    @Column("DB_NAME")
    val dbName: String,
    
    @Column("DB_URL")
    val dbUrl: String?,
    
    @Column("DB_PORT")
    val dbPort: Int?,
    
    @Column("DB_USERNAME")
    val dbUsername: String,
    
    @Column("CUSTOM_JDBC_URL")
    val customJdbcUrl: String?,
    
    @Column("ACTIVE_FLAG")
    val activeFlag: Boolean,
    
    @Column("CREATE_BY")
    val createBy: String?,
    
    @Column("CREATE_TIME")
    val createTime: LocalDateTime?,
    
    @Column("UPDATE_BY")
    val updateBy: String?,
    
    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime?,
    
    @Column("SYSTEM_ID")
    val systemId: Long?,
    
    @Column("DESCRIPTION")
    val description: String?
)

/**
 * 数据源匹配结果 (Datasource Matching Result)
 *
 * 包含匹配的数据源、未匹配的血缘数据源和未匹配的元数据数据源
 */
data class DatasourceMatchingResult(
    val matchedPairs: List<MatchedDatasourcePair>,
    val unmatchedLineageDatasources: List<LineageDatasource>,
    val unmatchedMetadataDataSources: List<MetadataDataSource>,
    val statistics: MatchingStatistics
)

/**
 * 匹配统计信息 (Matching Statistics)
 *
 * 提供匹配过程的详细统计数据
 */
data class MatchingStatistics(
    val totalLineageDatasources: Int,
    val totalMetadataDataSources: Int,
    val matchedPairsCount: Int,
    val unmatchedLineageDatasourcesCount: Int,
    val unmatchedMetadataDataSourcesCount: Int,
    val matchingRate: Double, // 匹配率 (matched pairs / total lineage datasources)
    val processingTimeMs: Long // 处理时间（毫秒）
)

/**
 * 匹配的数据源对 (Matched Datasource Pair)
 *
 * 表示一个匹配的血缘数据源和元数据数据源对
 */
data class MatchedDatasourcePair(
    val lineageDatasource: LineageDatasource,
    val metadataDataSource: MetadataDataSource
)

/**
 * 血缘数据源仓库接口 (Lineage Datasource Repository Interface)
 */
@Repository
interface LineageDatasourceRepository : CrudRepository<LineageDatasource, Long>

/**
 * 元数据数据源仓库接口 (Metadata Data Source Repository Interface)
 */
@Repository
interface MetadataDataSourceRepository : CrudRepository<MetadataDataSource, Long>

/**
 * 数据源匹配服务 (Datasource Matching Service)
 *
 * 负责匹配血缘数据源和元数据数据源
 */
@Service
class DatasourceMatchingService(
    private val jdbcTemplate: JdbcTemplate
) {
    private val logger = LoggerFactory.getLogger(DatasourceMatchingService::class.java)
    
    /**
     * 匹配血缘数据源和元数据数据源
     *
     * 匹配条件:
     * 1. LOWER(lineage_datasources.db_type) = LOWER(metadata_data_source.DB_TYPE)
     * 2. lineage_datasources.host = metadata_data_source.DB_URL
     * 3. lineage_datasources.port = COALESCE(metadata_data_source.DB_PORT, 3306)
     * 4. lineage_datasources.database_name = metadata_data_source.DB_NAME
     *
     * @return 数据源匹配结果
     */
    fun matchDatasources(): DatasourceMatchingResult {
        val startTime = System.currentTimeMillis()
        logger.info("a1b2c3d4 | 开始匹配血缘数据源和元数据数据源")

        // 查询所有血缘数据源
        val lineageDatasources = getAllLineageDatasources()
        logger.info("e5f6g7h8 | 查询到 ${lineageDatasources.size} 个血缘数据源")

        // 查询所有元数据数据源
        val metadataDataSources = getAllMetadataDataSources()
        logger.info("i9j0k1l2 | 查询到 ${metadataDataSources.size} 个元数据数据源")

        // 在内存中进行匹配
        val matchResult = performMatchingInMemory(lineageDatasources, metadataDataSources)

        val endTime = System.currentTimeMillis()
        val processingTime = endTime - startTime

        // 创建统计信息
        val statistics = MatchingStatistics(
            totalLineageDatasources = lineageDatasources.size,
            totalMetadataDataSources = metadataDataSources.size,
            matchedPairsCount = matchResult.matchedPairs.size,
            unmatchedLineageDatasourcesCount = matchResult.unmatchedLineageDatasources.size,
            unmatchedMetadataDataSourcesCount = matchResult.unmatchedMetadataDataSources.size,
            matchingRate = if (lineageDatasources.isNotEmpty()) {
                matchResult.matchedPairs.size.toDouble() / lineageDatasources.size
            } else 0.0,
            processingTimeMs = processingTime
        )

        val finalResult = matchResult.copy(statistics = statistics)

        logger.info("m3n4o5p6 | 匹配完成: 匹配成功 ${finalResult.matchedPairs.size} 对, " +
                "未匹配血缘数据源 ${finalResult.unmatchedLineageDatasources.size} 个, " +
                "未匹配元数据数据源 ${finalResult.unmatchedMetadataDataSources.size} 个, " +
                "匹配率 ${String.format("%.2f", statistics.matchingRate * 100)}%, " +
                "处理时间 ${processingTime}ms")

        return finalResult
    }
    
    /**
     * 在内存中执行匹配逻辑
     *
     * @param lineageDatasources 血缘数据源列表
     * @param metadataDataSources 元数据数据源列表
     * @return 数据源匹配结果（不包含统计信息）
     */
    private fun performMatchingInMemory(
        lineageDatasources: List<LineageDatasource>,
        metadataDataSources: List<MetadataDataSource>
    ): DatasourceMatchingResult {
        val matchedPairs = mutableListOf<MatchedDatasourcePair>()
        val matchedLineageDatasourceIds = mutableSetOf<Long>()
        val matchedMetadataDataSourceIds = mutableSetOf<Long>()

        // 遍历所有血缘数据源
        for (lineageDatasource in lineageDatasources) {
            logger.debug("q7r8s9t0 | 正在匹配血缘数据源: id=${lineageDatasource.id}, " +
                    "name=${lineageDatasource.datasourceName}, " +
                    "dbType=${lineageDatasource.dbType}, " +
                    "host=${lineageDatasource.host}, " +
                    "port=${lineageDatasource.port}, " +
                    "database=${lineageDatasource.databaseName}")

            // 查找匹配的元数据数据源
            val matchedMetadataDataSource = metadataDataSources.find { metadataDataSource ->
                // 匹配条件
                val dbTypeMatch = lineageDatasource.dbType.equals(metadataDataSource.dbType, ignoreCase = true)
                val hostMatch = lineageDatasource.host == metadataDataSource.dbUrl
                val portMatch = lineageDatasource.port == (metadataDataSource.dbPort ?: 3306)
                val databaseNameMatch = lineageDatasource.databaseName == metadataDataSource.dbName

                val isMatch = dbTypeMatch && hostMatch && portMatch && databaseNameMatch

                if (logger.isDebugEnabled && isMatch) {
                    logger.debug("u1v2w3x4 | 找到匹配: 血缘数据源 ${lineageDatasource.id} <-> 元数据数据源 ${metadataDataSource.id}")
                }

                isMatch
            }

            // 如果找到匹配，添加到匹配列表
            if (matchedMetadataDataSource != null) {
                matchedPairs.add(MatchedDatasourcePair(lineageDatasource, matchedMetadataDataSource))
                matchedLineageDatasourceIds.add(lineageDatasource.id!!)
                matchedMetadataDataSourceIds.add(matchedMetadataDataSource.id!!)

                logger.info("y5z6a7b8 | 成功匹配: 血缘数据源 '${lineageDatasource.datasourceName}' (ID: ${lineageDatasource.id}) " +
                        "<-> 元数据数据源 '${matchedMetadataDataSource.sourceName}' (ID: ${matchedMetadataDataSource.id})")
            } else {
                logger.warn("c9d0e1f2 | 未找到匹配的元数据数据源: 血缘数据源 '${lineageDatasource.datasourceName}' " +
                        "(ID: ${lineageDatasource.id}, dbType: ${lineageDatasource.dbType}, " +
                        "host: ${lineageDatasource.host}, port: ${lineageDatasource.port}, " +
                        "database: ${lineageDatasource.databaseName})")
            }
        }

        // 找出未匹配的血缘数据源
        val unmatchedLineageDatasources = lineageDatasources.filter {
            it.id !in matchedLineageDatasourceIds
        }

        // 找出未匹配的元数据数据源
        val unmatchedMetadataDataSources = metadataDataSources.filter {
            it.id !in matchedMetadataDataSourceIds
        }

        // 记录未匹配的元数据数据源
        unmatchedMetadataDataSources.forEach { metadataDataSource ->
            logger.warn("g3h4i5j6 | 未匹配的元数据数据源: '${metadataDataSource.sourceName}' " +
                    "(ID: ${metadataDataSource.id}, dbType: ${metadataDataSource.dbType}, " +
                    "dbUrl: ${metadataDataSource.dbUrl}, dbPort: ${metadataDataSource.dbPort}, " +
                    "dbName: ${metadataDataSource.dbName})")
        }

        return DatasourceMatchingResult(
            matchedPairs = matchedPairs,
            unmatchedLineageDatasources = unmatchedLineageDatasources,
            unmatchedMetadataDataSources = unmatchedMetadataDataSources,
            statistics = MatchingStatistics(0, 0, 0, 0, 0, 0.0, 0L) // 临时占位符，将在调用方法中替换
        )
    }
    
    /**
     * 查询所有血缘数据源
     *
     * @return 血缘数据源列表
     */
    private fun getAllLineageDatasources(): List<LineageDatasource> {
        val sql = """
            SELECT id, datasource_name, db_type, host, port, database_name, connection_string,
                   system_id, status, created_at, updated_at
            FROM lineage_datasources
            WHERE status = 'ACTIVE'
        """.trimIndent()
        
        return jdbcTemplate.query(sql, lineageDatasourceRowMapper)
    }
    
    /**
     * 查询所有元数据数据源
     *
     * @return 元数据数据源列表
     */
    private fun getAllMetadataDataSources(): List<MetadataDataSource> {
        val sql = """
            SELECT 
                ID, SOURCE_NAME, DB_TYPE, DB_DRIVER, DB_NAME,
                DB_URL, DB_PORT, DB_USERNAME, CUSTOM_JDBC_URL,
                ACTIVE_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, 
                UPDATE_TIME, SYSTEM_ID, DESCRIPTION
            FROM metadata_data_source
            WHERE ACTIVE_FLAG = 1
        """.trimIndent()
        
        return jdbcTemplate.query(sql, metadataDataSourceRowMapper)
    }
    
    /**
     * 血缘数据源行映射器
     */
    private val lineageDatasourceRowMapper = RowMapper<LineageDatasource> { rs, _ ->
        LineageDatasource(
            id = rs.getLong("id"),
            datasourceName = rs.getString("datasource_name"),
            dbType = rs.getString("db_type"),
            host = rs.getString("host"),
            port = rs.getInt("port"),
            databaseName = rs.getString("database_name"),
            connectionString = rs.getString("connection_string"),
            systemId = rs.getObject("system_id") as? Long,
            status = rs.getString("status"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime()
        )
    }
    
    /**
     * 元数据数据源行映射器
     */
    private val metadataDataSourceRowMapper = RowMapper<MetadataDataSource> { rs, _ ->
        MetadataDataSource(
            id = rs.getLong("ID"),
            sourceName = rs.getString("SOURCE_NAME"),
            dbType = rs.getString("DB_TYPE"),
            dbDriver = rs.getString("DB_DRIVER"),
            dbName = rs.getString("DB_NAME"),
            dbUrl = rs.getString("DB_URL"),
            dbPort = rs.getObject("DB_PORT") as? Int,
            dbUsername = rs.getString("DB_USERNAME"),
            customJdbcUrl = rs.getString("CUSTOM_JDBC_URL"),
            activeFlag = rs.getBoolean("ACTIVE_FLAG"),
            createBy = rs.getString("CREATE_BY"),
            createTime = rs.getTimestamp("CREATE_TIME")?.toLocalDateTime(),
            updateBy = rs.getString("UPDATE_BY"),
            updateTime = rs.getTimestamp("UPDATE_TIME")?.toLocalDateTime(),
            systemId = rs.getObject("SYSTEM_ID") as? Long,
            description = rs.getString("DESCRIPTION")
        )
    }

    /**
     * 根据血缘数据源ID查找匹配的元数据数据源
     *
     * @param lineageDatasourceId 血缘数据源ID
     * @return 匹配的元数据数据源，如果没有匹配则返回null
     */
    fun findMatchedMetadataDataSourceByLineageId(lineageDatasourceId: Long): MetadataDataSource? {
        val matchResult = matchDatasources()
        return matchResult.matchedPairs.find { it.lineageDatasource.id == lineageDatasourceId }?.metadataDataSource
    }

    /**
     * 根据元数据数据源ID查找匹配的血缘数据源
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 匹配的血缘数据源，如果没有匹配则返回null
     */
    fun findMatchedLineageDatasourceByMetadataId(metadataDataSourceId: Long): LineageDatasource? {
        val matchResult = matchDatasources()
        return matchResult.matchedPairs.find { it.metadataDataSource.id == metadataDataSourceId }?.lineageDatasource
    }

    /**
     * 获取匹配统计信息
     *
     * @return 匹配统计信息
     */
    fun getMatchingStatistics(): MatchingStatistics {
        return matchDatasources().statistics
    }

    /**
     * 检查特定血缘数据源是否有匹配的元数据数据源
     *
     * @param lineageDatasourceId 血缘数据源ID
     * @return 如果有匹配则返回true，否则返回false
     */
    fun hasMatchedMetadataDataSource(lineageDatasourceId: Long): Boolean {
        return findMatchedMetadataDataSourceByLineageId(lineageDatasourceId) != null
    }

    /**
     * 检查特定元数据数据源是否有匹配的血缘数据源
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 如果有匹配则返回true，否则返回false
     */
    fun hasMatchedLineageDatasource(metadataDataSourceId: Long): Boolean {
        return findMatchedLineageDatasourceByMetadataId(metadataDataSourceId) != null
    }
}
