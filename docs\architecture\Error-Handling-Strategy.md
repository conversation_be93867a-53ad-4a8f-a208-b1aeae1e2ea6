# Error Handling Strategy: "Error as Value" vs Exceptions

## Overview

This document defines the hybrid error handling strategy that combines "error as value" pattern with traditional exception handling, using `OperationResult<T>` as the primary mechanism.

## Core Principles

### 1. Error Classification

| Error Type | Handling Strategy | Example |
|------------|------------------|---------|
| **Expected Business Errors** | `OperationResult.failure()` | Validation failures, parsing errors |
| **Unexpected System Errors** | Throw exceptions | Database connection failures, OOM |
| **Recoverable Errors** | `OperationResult` with warnings | Partial parsing success |
| **Unrecoverable Errors** | Throw exceptions | Configuration errors, security violations |

### 2. Decision Matrix

```
Is the error expected in normal business flow?
├─ YES: Use OperationResult.failure()
└─ NO: Is the error recoverable?
   ├─ YES: Use OperationResult with warnings
   └─ NO: Throw exception
```

## Implementation Patterns

### Pattern 1: Expected Business Errors
```kotlin
fun validateScript(script: String): OperationResult<ValidationResult> {
    if (script.isBlank()) {
        return OperationResult.failure("脚本内容不能为空")
    }
    
    if (script.length > MAX_SCRIPT_LENGTH) {
        return OperationResult.failure("脚本长度超过限制: ${script.length} > $MAX_SCRIPT_LENGTH")
    }
    
    return OperationResult.success(ValidationResult(script))
}
```

### Pattern 2: Recoverable Errors with Warnings
```kotlin
fun parseMultipleSqlStatements(sql: String): OperationResult<List<ParsedStatement>> {
    val statements = sql.split(";")
    val parsed = mutableListOf<ParsedStatement>()
    val warnings = mutableListOf<String>()
    
    statements.forEach { statement ->
        try {
            parsed.add(parseStatement(statement))
        } catch (e: SqlParsingException) {
            warnings.add("无法解析SQL语句: ${statement.take(50)}... - ${e.message}")
        }
    }
    
    return if (parsed.isEmpty()) {
        OperationResult.failure("所有SQL语句解析失败", warnings = warnings)
    } else {
        OperationResult.partialSuccess(parsed, warnings)
    }
}
```

### Pattern 3: Exception Wrapping for Debugging
```kotlin
fun processWithExceptionHandling(): OperationResult<ProcessResult> {
    return OperationResult.catching(
        operation = {
            // 可能抛出异常的操作
            performComplexProcessing()
        },
        onException = { exception ->
            when (exception) {
                is SqlParsingException -> OperationResult.failure(
                    "SQL解析失败: ${exception.message}",
                    preserveException = true
                )
                is DatabaseException -> {
                    // 数据库异常应该重新抛出，让全局异常处理器处理
                    throw exception
                }
                else -> OperationResult.fromException(exception)
            }
        }
    )
}
```

### Pattern 4: Safe Exception Propagation
```kotlin
fun criticalSystemOperation(): ProcessResult {
    val result = OperationResult.catchingResult {
        performCriticalOperation()
    }
    
    // 如果是系统级错误，重新抛出异常
    if (!result.success && result.exception is SystemException) {
        result.throwIfFailed()
    }
    
    return result.data ?: throw IllegalStateException("操作失败但未抛出异常")
}
```

## Service Layer Guidelines

### When to Use OperationResult
1. **Business Logic Validation**
   ```kotlin
   fun validateLineageData(data: LineageData): OperationResult<ValidatedLineageData>
   ```

2. **Data Processing with Partial Success**
   ```kotlin
   fun processBatchData(items: List<DataItem>): OperationResult<BatchProcessResult>
   ```

3. **External API Integration** (when failures are expected)
   ```kotlin
   fun fetchMetadataFromExternalSystem(): OperationResult<MetadataResponse>
   ```

### When to Throw Exceptions
1. **Configuration Errors**
   ```kotlin
   fun initializeDatabase() {
       if (databaseUrl.isBlank()) {
           throw ConfigurationException("数据库URL未配置")
       }
   }
   ```

2. **System Resource Failures**
   ```kotlin
   fun connectToDatabase(): Connection {
       return dataSource.connection ?: throw DatabaseConnectionException("无法连接到数据库")
   }
   ```

3. **Security Violations**
   ```kotlin
   fun validateUserPermission(user: User, resource: Resource) {
       if (!hasPermission(user, resource)) {
           throw SecurityException("用户无权限访问资源")
       }
   }
   ```

## Controller Layer Integration

### Standard Pattern
```kotlin
@PostMapping("/analyze")
fun analyzeScript(@RequestBody request: ScriptAnalysisRequest): ResponseEntity<ApiResponse<DataLineage>> {
    return try {
        val result = scriptAnalysisService.analyzeScript(request)
        
        when {
            result.isCompleteSuccess() -> {
                logger.info("脚本分析完全成功: ${result.summarize()}")
                ResponseEntity.ok(ApiResponse.success(result.data, result.message))
            }
            result.isPartialSuccess() -> {
                logger.warn("脚本分析部分成功: ${result.summarize()}")
                ResponseEntity.ok(ApiResponse.success(result.data, "部分成功: ${result.warnings.size}个警告"))
            }
            else -> {
                logger.error("脚本分析失败: ${result.getDetailedErrorInfo()}")
                ResponseEntity.badRequest().body(ApiResponse.error(result.message ?: "分析失败"))
            }
        }
    } catch (e: Exception) {
        // 全局异常处理器会捕获这些异常
        throw e
    }
}
```

### Exception Handling in Controllers
```kotlin
@PostMapping("/critical-operation")
fun performCriticalOperation(@RequestBody request: CriticalRequest): ResponseEntity<ApiResponse<CriticalResult>> {
    // 让异常传播到全局异常处理器
    val result = criticalService.performOperation(request)
    return ResponseEntity.ok(ApiResponse.success(result))
}
```

## Testing Strategies

### Testing OperationResult
```kotlin
@Test
fun `should handle business validation errors`() {
    val result = service.validateInput("invalid-input")
    
    assertFalse(result.success)
    assertTrue(result.hasErrors())
    assertFalse(result.hasException()) // 业务错误不应该有异常
    assertEquals("输入验证失败", result.message)
}

@Test
fun `should preserve exception for debugging`() {
    val result = service.processWithPossibleException()
    
    if (!result.success && result.hasException()) {
        assertTrue(result.exception is SpecificException)
        assertEquals("具体错误信息", result.exception?.message)
    }
}
```

### Testing Exception Propagation
```kotlin
@Test
fun `should throw exception for system errors`() {
    assertThrows<SystemException> {
        service.performCriticalOperation()
    }
}
```

## Migration Guidelines

### Phase 1: Identify Error Types
1. Review existing try-catch blocks
2. Classify errors as business vs system errors
3. Identify recoverable vs unrecoverable errors

### Phase 2: Gradual Migration
```kotlin
// Before
fun oldMethod(): ScriptLineageResult {
    return try {
        val result = process()
        ScriptLineageResult(result, warnings, emptyList(), true)
    } catch (e: Exception) {
        ScriptLineageResult(null, warnings, listOf(e.message), false)
    }
}

// After
fun newMethod(): OperationResult<DataLineage> {
    return OperationResult.catching(
        operation = { process() },
        onException = { exception ->
            when (exception) {
                is BusinessException -> OperationResult.failure(exception.message, warnings = warnings)
                is SystemException -> throw exception // 让全局处理器处理
                else -> OperationResult.fromException(exception, warnings = warnings)
            }
        }
    )
}
```

### Phase 3: Update Controllers
```kotlin
// 更新控制器以使用新的错误处理模式
@PostMapping("/endpoint")
fun endpoint(): ResponseEntity<ApiResponse<ResultType>> {
    val result = service.newMethod()
    return if (result.success) {
        ResponseEntity.ok(ApiResponse.success(result.data))
    } else {
        ResponseEntity.badRequest().body(ApiResponse.error(result.message))
    }
}
```

## Best Practices

1. **Consistent Error Messages**: Use clear, actionable error messages
2. **Preserve Context**: Include relevant context in error messages
3. **Log Appropriately**: Use different log levels for different error types
4. **Document Exceptions**: Clearly document which methods throw exceptions
5. **Test Both Paths**: Test both success and failure scenarios
6. **Monitor Errors**: Track error patterns in production

## Anti-Patterns to Avoid

1. **Silent Failures**: Never ignore errors or return null without indication
2. **Exception Swallowing**: Don't catch exceptions without proper handling
3. **Mixed Patterns**: Don't mix OperationResult and exceptions inconsistently
4. **Over-wrapping**: Don't wrap every operation in OperationResult unnecessarily
5. **Under-logging**: Don't fail to log important error information

## Conclusion

This hybrid approach provides:
- **Flexibility**: Handle different error types appropriately
- **Debugging**: Preserve exception information when needed
- **Consistency**: Standardized error handling across the codebase
- **Performance**: Avoid exception overhead for expected errors
- **Maintainability**: Clear patterns for different scenarios
