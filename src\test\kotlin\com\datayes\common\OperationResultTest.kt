package com.datayes.common

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.*

/**
 * OperationResult 测试类 (OperationResult Test Class)
 * 
 * 全面测试 OperationResult 的各种功能和边界情况
 */
class OperationResultTest {

    // 测试数据类
    data class TestData(val value: String, val count: Int)
    data class TransformedData(val transformedValue: String)

    @Test
    fun `should create successful result with data`() {
        val data = TestData("test", 42)
        val result = OperationResult.success(data, "Operation completed")

        assertTrue(result.success)
        assertEquals(data, result.data)
        assertEquals("Operation completed", result.message)
        assertTrue(result.warnings.isEmpty())
        assertTrue(result.errors.isEmpty())
        assertTrue(result.isCompleteSuccess())
        assertFalse(result.isPartialSuccess())
    }

    @Test
    fun `should create successful result with warnings`() {
        val data = TestData("test", 42)
        val warnings = listOf("Warning 1", "Warning 2")
        val result = OperationResult.success(data, "Operation completed", warnings)

        assertTrue(result.success)
        assertEquals(data, result.data)
        assertEquals(warnings, result.warnings)
        assertTrue(result.errors.isEmpty())
        assertFalse(result.isCompleteSuccess())
        assertTrue(result.isPartialSuccess())
        assertTrue(result.hasWarnings())
        assertFalse(result.hasErrors())
    }

    @Test
    fun `should create partial success result`() {
        val data = TestData("test", 42)
        val warnings = listOf("Some issues occurred")
        val result = OperationResult.partialSuccess(data, warnings, "Partially completed")

        assertTrue(result.success)
        assertEquals(data, result.data)
        assertEquals(warnings, result.warnings)
        assertTrue(result.errors.isEmpty())
        assertEquals("Partially completed", result.message)
        assertTrue(result.isPartialSuccess())
        assertFalse(result.isCompleteSuccess())
    }

    @Test
    fun `should create failure result with single error`() {
        val result = OperationResult.failure<TestData>("Operation failed")

        assertFalse(result.success)
        assertNull(result.data)
        assertEquals("Operation failed", result.message)
        assertEquals(listOf("Operation failed"), result.errors)
        assertTrue(result.warnings.isEmpty())
        assertFalse(result.isCompleteSuccess())
        assertFalse(result.isPartialSuccess())
        assertTrue(result.hasErrors())
        assertFalse(result.hasWarnings())
    }

    @Test
    fun `should create failure result with multiple errors`() {
        val errors = listOf("Error 1", "Error 2", "Error 3")
        val warnings = listOf("Warning 1")
        val result = OperationResult.failure<TestData>(errors, "Multiple errors occurred", warnings)

        assertFalse(result.success)
        assertNull(result.data)
        assertEquals("Multiple errors occurred", result.message)
        assertEquals(errors, result.errors)
        assertEquals(warnings, result.warnings)
        assertTrue(result.hasErrors())
        assertTrue(result.hasWarnings())
    }

    @Test
    fun `should create failure result from exception`() {
        val exception = RuntimeException("Test exception")
        val warnings = listOf("Warning before exception")
        val result = OperationResult.fromException<TestData>(exception, "Custom error message", warnings)

        assertFalse(result.success)
        assertNull(result.data)
        assertEquals("Custom error message", result.message)
        assertEquals(listOf("Custom error message"), result.errors)
        assertEquals(warnings, result.warnings)
    }

    @Test
    fun `should create failure result from exception with default message`() {
        val exception = RuntimeException("Exception message")
        val result = OperationResult.fromException<TestData>(exception)

        assertFalse(result.success)
        assertEquals("Exception message", result.message)
        assertEquals(listOf("Exception message"), result.errors)
        assertEquals(exception, result.exception)
        assertTrue(result.hasException())
    }

    @Test
    fun `should create failure result from exception without preserving exception`() {
        val exception = RuntimeException("Exception message")
        val result = OperationResult.fromException<TestData>(exception, preserveException = false)

        assertFalse(result.success)
        assertEquals("Exception message", result.message)
        assertNull(result.exception)
        assertFalse(result.hasException())
    }

    @Test
    fun `should create conditional result based on condition`() {
        val data = TestData("test", 42)
        
        // Success case
        val successResult = OperationResult.conditional(
            condition = true,
            data = data,
            successMessage = "Condition met",
            failureMessage = "Condition not met"
        )
        
        assertTrue(successResult.success)
        assertEquals(data, successResult.data)
        assertEquals("Condition met", successResult.message)
        
        // Failure case
        val failureResult = OperationResult.conditional(
            condition = false,
            data = data,
            successMessage = "Condition met",
            failureMessage = "Condition not met"
        )
        
        assertFalse(failureResult.success)
        assertNull(failureResult.data)
        assertEquals("Condition not met", failureResult.message)
        assertEquals(listOf("Condition not met"), failureResult.errors)
    }

    @Test
    fun `should get all issues correctly`() {
        val warnings = listOf("Warning 1", "Warning 2")
        val errors = listOf("Error 1", "Error 2")
        val result = OperationResult<TestData>(
            success = false,
            warnings = warnings,
            errors = errors
        )

        val allIssues = result.getAllIssues()
        assertEquals(4, allIssues.size)
        assertEquals(warnings + errors, allIssues)
    }

    @Test
    fun `should generate correct summary messages`() {
        // Complete success
        val completeSuccess = OperationResult.success(TestData("test", 1))
        assertEquals("操作完全成功", completeSuccess.summarize())

        // Partial success
        val partialSuccess = OperationResult.success(TestData("test", 1), warnings = listOf("Warning"))
        assertEquals("操作部分成功，有 1 个警告", partialSuccess.summarize())

        // Success with errors (edge case)
        val successWithErrors = OperationResult<TestData>(
            success = true,
            data = TestData("test", 1),
            warnings = listOf("Warning"),
            errors = listOf("Error")
        )
        assertEquals("操作成功，但有 1 个错误和 1 个警告", successWithErrors.summarize())

        // Failure
        val failure = OperationResult.failure<TestData>("Failed", listOf("Error 1", "Error 2"), listOf("Warning"))
        assertEquals("操作失败，有 2 个错误和 1 个警告", failure.summarize())
    }

    @Test
    fun `should map result data correctly`() {
        val originalData = TestData("test", 42)
        val originalResult = OperationResult.success(originalData, "Success", listOf("Warning"))

        val mappedResult = originalResult.map { data ->
            TransformedData("transformed_${data.value}")
        }

        assertTrue(mappedResult.success)
        assertEquals(TransformedData("transformed_test"), mappedResult.data)
        assertEquals("Success", mappedResult.message)
        assertEquals(listOf("Warning"), mappedResult.warnings)
        assertTrue(mappedResult.errors.isEmpty())
    }

    @Test
    fun `should map null data correctly`() {
        val originalResult = OperationResult.failure<TestData>("Failed")

        val mappedResult = originalResult.map { data ->
            TransformedData("transformed_${data.value}")
        }

        assertFalse(mappedResult.success)
        assertNull(mappedResult.data)
        assertEquals("Failed", mappedResult.message)
    }

    @Test
    fun `should flatMap result correctly`() {
        val originalData = TestData("test", 42)
        val originalResult = OperationResult.success(originalData, "Success", listOf("Warning 1"))

        val flatMappedResult = originalResult.flatMap { data ->
            OperationResult.success(
                TransformedData("transformed_${data.value}"),
                "Transformed",
                listOf("Warning 2")
            )
        }

        assertTrue(flatMappedResult.success)
        assertEquals(TransformedData("transformed_test"), flatMappedResult.data)
        assertEquals("Transformed", flatMappedResult.message)
        assertEquals(listOf("Warning 1", "Warning 2"), flatMappedResult.warnings)
    }

    @Test
    fun `should flatMap failure correctly`() {
        val originalResult = OperationResult.failure<TestData>("Original failure", listOf("Error 1"), listOf("Warning 1"))

        val flatMappedResult = originalResult.flatMap { data ->
            OperationResult.success(TransformedData("transformed"))
        }

        assertFalse(flatMappedResult.success)
        assertNull(flatMappedResult.data)
        assertEquals("Original failure", flatMappedResult.message)
        assertEquals(listOf("Error 1"), flatMappedResult.errors)
        assertEquals(listOf("Warning 1"), flatMappedResult.warnings)
    }

    @Test
    fun `should combine multiple successful results`() {
        val results = listOf(
            OperationResult.success(TestData("test1", 1), warnings = listOf("Warning 1")),
            OperationResult.success(TestData("test2", 2), warnings = listOf("Warning 2")),
            OperationResult.success(TestData("test3", 3))
        )

        val combinedResult = OperationResult.combine(results) { dataList ->
            dataList.map { it.value }.joinToString(",")
        }

        assertTrue(combinedResult.success)
        assertEquals("test1,test2,test3", combinedResult.data)
        assertEquals(listOf("Warning 1", "Warning 2"), combinedResult.warnings)
        assertTrue(combinedResult.errors.isEmpty())
        assertEquals("所有操作成功完成", combinedResult.message)
    }

    @Test
    fun `should combine results with failures`() {
        val results = listOf(
            OperationResult.success(TestData("test1", 1)),
            OperationResult.failure<TestData>("Failed", listOf("Error 1")),
            OperationResult.success(TestData("test3", 3), warnings = listOf("Warning 1"))
        )

        val combinedResult = OperationResult.combine(results) { dataList ->
            dataList.map { it.value }.joinToString(",")
        }

        assertFalse(combinedResult.success)
        assertNull(combinedResult.data)
        assertEquals(listOf("Error 1"), combinedResult.errors)
        assertEquals(listOf("Warning 1"), combinedResult.warnings)
        assertEquals("部分或全部操作失败", combinedResult.message)
    }

    @Test
    fun `should handle empty results list in combine`() {
        val results = emptyList<OperationResult<TestData>>()

        val combinedResult = OperationResult.combine(results) { dataList ->
            dataList.size.toString()
        }

        assertTrue(combinedResult.success)
        assertEquals("0", combinedResult.data)
        assertTrue(combinedResult.warnings.isEmpty())
        assertTrue(combinedResult.errors.isEmpty())
    }

    @Test
    fun `should handle edge cases in utility methods`() {
        // Empty warnings and errors
        val emptyResult = OperationResult<TestData>(success = true, data = TestData("test", 1))
        assertFalse(emptyResult.hasWarnings())
        assertFalse(emptyResult.hasErrors())
        assertTrue(emptyResult.getAllIssues().isEmpty())

        // Null data with success = true (edge case)
        val nullDataResult = OperationResult<TestData>(success = true, data = null)
        assertTrue(nullDataResult.success)
        assertNull(nullDataResult.data)
    }

    @Test
    fun `should handle exception utility methods`() {
        val rootException = IllegalArgumentException("Root cause")
        val wrappedException = RuntimeException("Wrapper", rootException)
        val result = OperationResult.fromException<TestData>(wrappedException)

        assertTrue(result.hasException())
        assertEquals(wrappedException, result.exception)
        assertEquals(rootException, result.getRootCause())
        assertTrue(result.getDetailedErrorInfo().contains("RuntimeException"))
    }

    @Test
    fun `should throw exception when throwIfFailed is called on failed result with exception`() {
        val exception = RuntimeException("Test exception")
        val result = OperationResult.fromException<TestData>(exception)

        assertThrows<RuntimeException> {
            result.throwIfFailed()
        }
    }

    @Test
    fun `should not throw exception when throwIfFailed is called on successful result`() {
        val result = OperationResult.success(TestData("test", 1))

        // Should not throw
        result.throwIfFailed()
        assertTrue(result.success)
    }

    @Test
    fun `should safely execute operation with catching`() {
        // Success case
        val successResult = OperationResult.catching {
            TestData("success", 42)
        }

        assertTrue(successResult.success)
        assertEquals(TestData("success", 42), successResult.data)
        assertFalse(successResult.hasException())

        // Exception case
        val failureResult = OperationResult.catching<TestData> {
            throw RuntimeException("Test error")
        }

        assertFalse(failureResult.success)
        assertNull(failureResult.data)
        assertTrue(failureResult.hasException())
        assertEquals("Test error", failureResult.message)
    }

    @Test
    fun `should safely execute operation returning OperationResult with catchingResult`() {
        // Success case
        val successResult = OperationResult.catchingResult {
            OperationResult.success(TestData("success", 42))
        }

        assertTrue(successResult.success)
        assertEquals(TestData("success", 42), successResult.data)

        // Exception case
        val failureResult = OperationResult.catchingResult<TestData> {
            throw RuntimeException("Test error")
        }

        assertFalse(failureResult.success)
        assertTrue(failureResult.hasException())
    }

    @Test
    fun `should use custom exception handler in catching`() {
        val customResult = OperationResult.catching<TestData>(
            operation = { throw IllegalArgumentException("Invalid argument") },
            onException = { exception ->
                OperationResult.failure(
                    "Custom error: ${exception.message}",
                    errors = listOf("Validation failed"),
                    warnings = listOf("Check input parameters")
                )
            }
        )

        assertFalse(customResult.success)
        assertEquals("Custom error: Invalid argument", customResult.message)
        assertEquals(listOf("Validation failed"), customResult.errors)
        assertEquals(listOf("Check input parameters"), customResult.warnings)
        assertNull(customResult.exception) // Custom handler didn't preserve exception
    }
}
