package com.datayes.lineage

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 数据源匹配服务测试 (Datasource Matching Service Test)
 *
 * 测试数据源匹配逻辑的正确性
 */
@ExtendWith(MockitoExtension::class)
class DatasourceMatchingServiceTest {

    @Mock
    private lateinit var jdbcTemplate: JdbcTemplate

    @InjectMocks
    private lateinit var datasourceMatchingService: DatasourceMatchingService

    @Test
    fun `should match datasources correctly when all conditions are met`() {
        // Given: 准备测试数据
        val lineageDatasources = listOf(
            LineageDatasource(
                id = 1L,
                datasourceName = "test-lineage-ds",
                dbType = "mysql",
                host = "localhost",
                port = 3306,
                databaseName = "test_db",
                connectionString = "***********************************",
                systemId = 1L,
                status = "ACTIVE",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )

        val metadataDataSources = listOf(
            MetadataDataSource(
                id = 1L,
                sourceName = "test-metadata-ds",
                dbType = "mysql",
                dbDriver = "com.mysql.cj.jdbc.Driver",
                dbName = "test_db",
                dbUrl = "localhost",
                dbPort = 3306,
                dbUsername = "test_user",
                customJdbcUrl = null,
                activeFlag = true,
                createBy = "admin",
                createTime = LocalDateTime.now(),
                updateBy = null,
                updateTime = null,
                systemId = 1L,
                description = "Test metadata datasource"
            )
        )

        // Mock JDBC 查询结果
        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<LineageDatasource>>()
        )).thenReturn(lineageDatasources)

        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<MetadataDataSource>>()
        )).thenReturn(metadataDataSources)

        // When: 执行匹配
        val result = datasourceMatchingService.matchDatasources()

        // Then: 验证结果
        assertNotNull(result)
        assertEquals(1, result.matchedPairs.size)
        assertEquals(0, result.unmatchedLineageDatasources.size)
        assertEquals(0, result.unmatchedMetadataDataSources.size)
        
        val matchedPair = result.matchedPairs.first()
        assertEquals(1L, matchedPair.lineageDatasource.id)
        assertEquals(1L, matchedPair.metadataDataSource.id)
        
        // 验证统计信息
        val statistics = result.statistics
        assertEquals(1, statistics.totalLineageDatasources)
        assertEquals(1, statistics.totalMetadataDataSources)
        assertEquals(1, statistics.matchedPairsCount)
        assertEquals(0, statistics.unmatchedLineageDatasourcesCount)
        assertEquals(0, statistics.unmatchedMetadataDataSourcesCount)
        assertEquals(1.0, statistics.matchingRate)
        assertTrue(statistics.processingTimeMs >= 0)
    }

    @Test
    fun `should handle unmatched datasources correctly`() {
        // Given: 准备不匹配的测试数据
        val lineageDatasources = listOf(
            LineageDatasource(
                id = 1L,
                datasourceName = "unmatched-lineage-ds",
                dbType = "mysql",
                host = "localhost",
                port = 3306,
                databaseName = "unmatched_db",
                connectionString = "****************************************",
                systemId = 1L,
                status = "ACTIVE",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )

        val metadataDataSources = listOf(
            MetadataDataSource(
                id = 1L,
                sourceName = "unmatched-metadata-ds",
                dbType = "postgresql",
                dbDriver = "org.postgresql.Driver",
                dbName = "different_db",
                dbUrl = "different-host",
                dbPort = 5432,
                dbUsername = "test_user",
                customJdbcUrl = null,
                activeFlag = true,
                createBy = "admin",
                createTime = LocalDateTime.now(),
                updateBy = null,
                updateTime = null,
                systemId = 1L,
                description = "Unmatched metadata datasource"
            )
        )

        // Mock JDBC 查询结果
        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<LineageDatasource>>()
        )).thenReturn(lineageDatasources)

        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<MetadataDataSource>>()
        )).thenReturn(metadataDataSources)

        // When: 执行匹配
        val result = datasourceMatchingService.matchDatasources()

        // Then: 验证结果
        assertNotNull(result)
        assertEquals(0, result.matchedPairs.size)
        assertEquals(1, result.unmatchedLineageDatasources.size)
        assertEquals(1, result.unmatchedMetadataDataSources.size)
        
        assertEquals(1L, result.unmatchedLineageDatasources.first().id)
        assertEquals(1L, result.unmatchedMetadataDataSources.first().id)
        
        // 验证统计信息
        val statistics = result.statistics
        assertEquals(1, statistics.totalLineageDatasources)
        assertEquals(1, statistics.totalMetadataDataSources)
        assertEquals(0, statistics.matchedPairsCount)
        assertEquals(1, statistics.unmatchedLineageDatasourcesCount)
        assertEquals(1, statistics.unmatchedMetadataDataSourcesCount)
        assertEquals(0.0, statistics.matchingRate)
    }

    @Test
    fun `should handle empty datasource lists correctly`() {
        // Given: 空的数据源列表
        val emptyLineageDatasources = emptyList<LineageDatasource>()
        val emptyMetadataDataSources = emptyList<MetadataDataSource>()

        // Mock JDBC 查询结果
        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<LineageDatasource>>()
        )).thenReturn(emptyLineageDatasources)

        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<MetadataDataSource>>()
        )).thenReturn(emptyMetadataDataSources)

        // When: 执行匹配
        val result = datasourceMatchingService.matchDatasources()

        // Then: 验证结果
        assertNotNull(result)
        assertEquals(0, result.matchedPairs.size)
        assertEquals(0, result.unmatchedLineageDatasources.size)
        assertEquals(0, result.unmatchedMetadataDataSources.size)
        
        // 验证统计信息
        val statistics = result.statistics
        assertEquals(0, statistics.totalLineageDatasources)
        assertEquals(0, statistics.totalMetadataDataSources)
        assertEquals(0, statistics.matchedPairsCount)
        assertEquals(0, statistics.unmatchedLineageDatasourcesCount)
        assertEquals(0, statistics.unmatchedMetadataDataSourcesCount)
        assertEquals(0.0, statistics.matchingRate)
    }

    @Test
    fun `should match with default port when metadata port is null`() {
        // Given: 元数据数据源的端口为null，应该使用默认端口3306
        val lineageDatasources = listOf(
            LineageDatasource(
                id = 1L,
                datasourceName = "test-lineage-ds",
                dbType = "mysql",
                host = "localhost",
                port = 3306, // 使用默认端口
                databaseName = "test_db",
                connectionString = "***********************************",
                systemId = 1L,
                status = "ACTIVE",
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        )

        val metadataDataSources = listOf(
            MetadataDataSource(
                id = 1L,
                sourceName = "test-metadata-ds",
                dbType = "mysql",
                dbDriver = "com.mysql.cj.jdbc.Driver",
                dbName = "test_db",
                dbUrl = "localhost",
                dbPort = null, // 端口为null，应该使用默认值3306
                dbUsername = "test_user",
                customJdbcUrl = null,
                activeFlag = true,
                createBy = "admin",
                createTime = LocalDateTime.now(),
                updateBy = null,
                updateTime = null,
                systemId = 1L,
                description = "Test metadata datasource with null port"
            )
        )

        // Mock JDBC 查询结果
        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<LineageDatasource>>()
        )).thenReturn(lineageDatasources)

        whenever(jdbcTemplate.query(
            org.mockito.kotlin.any<String>(),
            org.mockito.kotlin.any<RowMapper<MetadataDataSource>>()
        )).thenReturn(metadataDataSources)

        // When: 执行匹配
        val result = datasourceMatchingService.matchDatasources()

        // Then: 验证结果 - 应该成功匹配
        assertNotNull(result)
        assertEquals(1, result.matchedPairs.size)
        assertEquals(0, result.unmatchedLineageDatasources.size)
        assertEquals(0, result.unmatchedMetadataDataSources.size)
        
        val matchedPair = result.matchedPairs.first()
        assertEquals(1L, matchedPair.lineageDatasource.id)
        assertEquals(1L, matchedPair.metadataDataSource.id)
    }
}
