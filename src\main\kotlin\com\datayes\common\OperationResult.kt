package com.datayes.common

/**
 * 通用操作结果包装器 (Generic Operation Result Wrapper)
 *
 * 标准化API响应格式，提供一致的成功/失败状态、警告信息、错误信息和结果数据结构。
 * 可以替代现有的各种特定响应类，如ScriptLineageResult、LineageResult、LineageOperationResponse等。
 *
 * ## 设计原则 (Design Principles)
 *
 * ### "Error as Value" vs Exception 混合策略 (Hybrid Strategy)
 * 1. **预期的业务错误** - 使用 OperationResult.failure() (Expected business errors)
 * 2. **意外的系统错误** - 抛出异常，由全局异常处理器处理 (Unexpected system errors - throw exceptions)
 * 3. **可恢复的错误** - 使用 OperationResult 包装 (Recoverable errors)
 * 4. **不可恢复的错误** - 抛出异常 (Unrecoverable errors)
 *
 * ### 使用指南 (Usage Guidelines)
 * - **验证失败**: OperationResult.failure("验证失败")
 * - **数据库连接失败**: throw DatabaseConnectionException()
 * - **解析错误但可继续**: OperationResult.partialSuccess(data, warnings)
 * - **配置错误**: throw ConfigurationException()
 *
 * @param T 结果数据的类型 (Type of the result data)
 * @property success 操作是否成功 (Whether the operation was successful)
 * @property data 操作结果数据，成功时通常不为null (Operation result data, usually not null when successful)
 * @property warnings 警告信息列表，非关键问题 (List of warning messages for non-critical issues)
 * @property errors 错误信息列表，关键问题 (List of error messages for critical issues)
 * @property message 可选的操作消息 (Optional operation message)
 * @property exception 可选的异常信息，用于调试 (Optional exception for debugging)
 */
data class OperationResult<T>(
    val success: Boolean,
    val data: T? = null,
    val warnings: List<String> = emptyList(),
    val errors: List<String> = emptyList(),
    val message: String? = null,
    val exception: Throwable? = null
) {
    
    /**
     * 检查是否有警告信息 (Check if there are warnings)
     */
    fun hasWarnings(): Boolean = warnings.isNotEmpty()
    
    /**
     * 检查是否有错误信息 (Check if there are errors)
     */
    fun hasErrors(): Boolean = errors.isNotEmpty()
    
    /**
     * 检查是否为部分成功（有警告但无错误）(Check if partially successful - has warnings but no errors)
     */
    fun isPartialSuccess(): Boolean = success && hasWarnings() && !hasErrors()
    
    /**
     * 检查是否为完全成功（无警告无错误）(Check if completely successful - no warnings or errors)
     */
    fun isCompleteSuccess(): Boolean = success && !hasWarnings() && !hasErrors()

    /**
     * 获取所有问题信息（警告+错误）(Get all issue messages - warnings + errors)
     */
    fun getAllIssues(): List<String> = warnings + errors

    /**
     * 检查是否包含异常信息 (Check if contains exception information)
     */
    fun hasException(): Boolean = exception != null

    /**
     * 获取异常的根本原因 (Get root cause of exception)
     */
    fun getRootCause(): Throwable? {
        var cause = exception
        while (cause?.cause != null) {
            cause = cause.cause
        }
        return cause
    }

    /**
     * 如果操作失败且有异常，则重新抛出异常 (Rethrow exception if operation failed and has exception)
     *
     * 用于在需要异常传播的场景中使用
     */
    fun throwIfFailed() {
        if (!success && exception != null) {
            throw exception
        }
    }

    /**
     * 获取用于日志记录的详细错误信息 (Get detailed error information for logging)
     */
    fun getDetailedErrorInfo(): String {
        val issues = getAllIssues()
        val exceptionInfo = exception?.let { " | Exception: ${it.javaClass.simpleName}: ${it.message}" } ?: ""
        return "Success: $success, Issues: ${issues.size}$exceptionInfo"
    }
    
    /**
     * 生成操作摘要信息 (Generate operation summary)
     */
    fun summarize(): String {
        return when {
            isCompleteSuccess() -> "操作完全成功"
            isPartialSuccess() -> "操作部分成功，有 ${warnings.size} 个警告"
            success && hasErrors() -> "操作成功，但有 ${errors.size} 个错误和 ${warnings.size} 个警告"
            else -> "操作失败，有 ${errors.size} 个错误和 ${warnings.size} 个警告"
        }
    }
    
    /**
     * 转换结果数据类型 (Transform result data type)
     * 
     * @param transform 转换函数 (Transform function)
     * @return 转换后的OperationResult (Transformed OperationResult)
     */
    fun <R> map(transform: (T) -> R): OperationResult<R> {
        return OperationResult(
            success = success,
            data = data?.let(transform),
            warnings = warnings,
            errors = errors,
            message = message
        )
    }
    
    /**
     * 转换结果数据类型，支持可能失败的转换 (Transform result data type with possible failure)
     * 
     * @param transform 转换函数，返回OperationResult (Transform function returning OperationResult)
     * @return 转换后的OperationResult (Transformed OperationResult)
     */
    fun <R> flatMap(transform: (T) -> OperationResult<R>): OperationResult<R> {
        return if (success && data != null) {
            val transformed = transform(data)
            OperationResult(
                success = transformed.success,
                data = transformed.data,
                warnings = warnings + transformed.warnings,
                errors = errors + transformed.errors,
                message = transformed.message ?: message
            )
        } else {
            OperationResult(
                success = false,
                data = null,
                warnings = warnings,
                errors = errors,
                message = message
            )
        }
    }
    
    companion object {
        
        /**
         * 创建成功结果 (Create successful result)
         * 
         * @param data 结果数据 (Result data)
         * @param message 可选消息 (Optional message)
         * @param warnings 可选警告列表 (Optional warnings list)
         * @return 成功的OperationResult (Successful OperationResult)
         */
        fun <T> success(
            data: T,
            message: String? = null,
            warnings: List<String> = emptyList()
        ): OperationResult<T> {
            return OperationResult(
                success = true,
                data = data,
                warnings = warnings,
                errors = emptyList(),
                message = message
            )
        }
        
        /**
         * 创建失败结果 (Create failure result)
         * 
         * @param message 错误消息 (Error message)
         * @param errors 错误列表 (Error list)
         * @param warnings 可选警告列表 (Optional warnings list)
         * @return 失败的OperationResult (Failed OperationResult)
         */
        fun <T> failure(
            message: String,
            errors: List<String> = listOf(message),
            warnings: List<String> = emptyList()
        ): OperationResult<T> {
            return OperationResult(
                success = false,
                data = null,
                warnings = warnings,
                errors = errors,
                message = message
            )
        }
        
        /**
         * 创建失败结果（多个错误）(Create failure result with multiple errors)
         * 
         * @param errors 错误列表 (Error list)
         * @param message 可选主要消息 (Optional main message)
         * @param warnings 可选警告列表 (Optional warnings list)
         * @return 失败的OperationResult (Failed OperationResult)
         */
        fun <T> failure(
            errors: List<String>,
            message: String? = null,
            warnings: List<String> = emptyList()
        ): OperationResult<T> {
            return OperationResult(
                success = false,
                data = null,
                warnings = warnings,
                errors = errors,
                message = message ?: errors.firstOrNull() ?: "操作失败"
            )
        }
        
        /**
         * 创建部分成功结果（有数据但有警告）(Create partial success result with data but warnings)
         * 
         * @param data 结果数据 (Result data)
         * @param warnings 警告列表 (Warnings list)
         * @param message 可选消息 (Optional message)
         * @return 部分成功的OperationResult (Partially successful OperationResult)
         */
        fun <T> partialSuccess(
            data: T,
            warnings: List<String>,
            message: String? = null
        ): OperationResult<T> {
            return OperationResult(
                success = true,
                data = data,
                warnings = warnings,
                errors = emptyList(),
                message = message
            )
        }
        
        /**
         * 从异常创建失败结果 (Create failure result from exception)
         *
         * 用于将捕获的异常转换为OperationResult，保留异常信息用于调试
         *
         * @param exception 异常对象 (Exception object)
         * @param message 可选自定义消息 (Optional custom message)
         * @param warnings 可选警告列表 (Optional warnings list)
         * @param preserveException 是否保留异常对象用于调试 (Whether to preserve exception for debugging)
         * @return 失败的OperationResult (Failed OperationResult)
         */
        fun <T> fromException(
            exception: Throwable,
            message: String? = null,
            warnings: List<String> = emptyList(),
            preserveException: Boolean = true
        ): OperationResult<T> {
            val errorMessage = message ?: exception.message ?: "未知错误 (Unknown error)"
            return OperationResult(
                success = false,
                data = null,
                warnings = warnings,
                errors = listOf(errorMessage),
                message = errorMessage,
                exception = if (preserveException) exception else null
            )
        }

        /**
         * 安全执行操作，捕获异常并转换为OperationResult (Safely execute operation, catch exceptions and convert to OperationResult)
         *
         * 用于包装可能抛出异常的操作，将异常转换为失败结果
         *
         * @param operation 要执行的操作 (Operation to execute)
         * @param onException 异常处理函数 (Exception handler function)
         * @return OperationResult (OperationResult)
         */
        inline fun <T> catching(
            operation: () -> T,
            onException: (Throwable) -> OperationResult<T> = { fromException(it) }
        ): OperationResult<T> {
            return try {
                success(operation())
            } catch (e: Throwable) {
                onException(e)
            }
        }

        /**
         * 安全执行返回OperationResult的操作 (Safely execute operation that returns OperationResult)
         *
         * @param operation 要执行的操作 (Operation to execute)
         * @param onException 异常处理函数 (Exception handler function)
         * @return OperationResult (OperationResult)
         */
        inline fun <T> catchingResult(
            operation: () -> OperationResult<T>,
            onException: (Throwable) -> OperationResult<T> = { fromException(it) }
        ): OperationResult<T> {
            return try {
                operation()
            } catch (e: Throwable) {
                onException(e)
            }
        }
        
        /**
         * 根据条件创建结果 (Create result based on condition)
         * 
         * @param condition 成功条件 (Success condition)
         * @param data 结果数据 (Result data)
         * @param successMessage 成功消息 (Success message)
         * @param failureMessage 失败消息 (Failure message)
         * @param warnings 可选警告列表 (Optional warnings list)
         * @return OperationResult (OperationResult)
         */
        fun <T> conditional(
            condition: Boolean,
            data: T?,
            successMessage: String? = null,
            failureMessage: String = "操作失败",
            warnings: List<String> = emptyList()
        ): OperationResult<T> {
            return if (condition) {
                OperationResult(
                    success = true,
                    data = data,
                    warnings = warnings,
                    errors = emptyList(),
                    message = successMessage
                )
            } else {
                OperationResult(
                    success = false,
                    data = null,
                    warnings = warnings,
                    errors = listOf(failureMessage),
                    message = failureMessage
                )
            }
        }
        
        /**
         * 合并多个OperationResult (Combine multiple OperationResults)
         * 
         * 所有结果都成功时才算成功，合并所有警告和错误
         * 
         * @param results 结果列表 (List of results)
         * @param combineData 数据合并函数 (Data combination function)
         * @return 合并后的OperationResult (Combined OperationResult)
         */
        fun <T, R> combine(
            results: List<OperationResult<T>>,
            combineData: (List<T>) -> R
        ): OperationResult<R> {
            val allWarnings = results.flatMap { it.warnings }
            val allErrors = results.flatMap { it.errors }
            val allSuccessful = results.all { it.success }
            val allData = results.mapNotNull { it.data }
            
            return if (allSuccessful && allData.size == results.size) {
                OperationResult(
                    success = true,
                    data = combineData(allData),
                    warnings = allWarnings,
                    errors = allErrors,
                    message = "所有操作成功完成"
                )
            } else {
                OperationResult(
                    success = false,
                    data = null,
                    warnings = allWarnings,
                    errors = allErrors,
                    message = "部分或全部操作失败"
                )
            }
        }
    }
}
