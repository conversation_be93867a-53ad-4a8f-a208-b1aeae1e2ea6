package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 数据源匹配控制器 (Datasource Matching Controller)
 *
 * 提供数据源匹配相关的 REST API 端点
 */
@RestController
@RequestMapping("/api/datasource-matching")
class DatasourceMatchingController(
    private val datasourceMatchingService: DatasourceMatchingService
) {
    
    private val logger = LoggerFactory.getLogger(DatasourceMatchingController::class.java)
    
    /**
     * 执行数据源匹配
     *
     * @return 数据源匹配结果
     */
    @GetMapping("/match")
    fun matchDatasources(): ResponseEntity<DatasourceMatchingResult> {
        logger.info("k7l8m9n0 | 收到数据源匹配请求")
        
        return try {
            val result = datasourceMatchingService.matchDatasources()
            logger.info("p1q2r3s4 | 数据源匹配完成，返回结果")
            ResponseEntity.ok(result)
        } catch (e: Exception) {
            logger.error("t5u6v7w8 | 数据源匹配过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * 获取匹配统计信息
     *
     * @return 匹配统计信息
     */
    @GetMapping("/statistics")
    fun getMatchingStatistics(): ResponseEntity<MatchingStatistics> {
        logger.info("x9y0z1a2 | 收到获取匹配统计信息请求")
        
        return try {
            val statistics = datasourceMatchingService.getMatchingStatistics()
            logger.info("b3c4d5e6 | 获取匹配统计信息完成")
            ResponseEntity.ok(statistics)
        } catch (e: Exception) {
            logger.error("f7g8h9i0 | 获取匹配统计信息过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * 根据血缘数据源ID查找匹配的元数据数据源
     *
     * @param lineageDatasourceId 血缘数据源ID
     * @return 匹配的元数据数据源，如果没有匹配则返回404
     */
    @GetMapping("/lineage/{lineageDatasourceId}/matched-metadata")
    fun findMatchedMetadataDataSource(
        @PathVariable lineageDatasourceId: Long
    ): ResponseEntity<MetadataDataSource> {
        logger.info("j1k2l3m4 | 查找血缘数据源 $lineageDatasourceId 匹配的元数据数据源")
        
        return try {
            val metadataDataSource = datasourceMatchingService
                .findMatchedMetadataDataSourceByLineageId(lineageDatasourceId)
            
            if (metadataDataSource != null) {
                logger.info("n5o6p7q8 | 找到匹配的元数据数据源: ${metadataDataSource.id}")
                ResponseEntity.ok(metadataDataSource)
            } else {
                logger.info("r9s0t1u2 | 未找到血缘数据源 $lineageDatasourceId 匹配的元数据数据源")
                ResponseEntity.notFound().build()
            }
        } catch (e: Exception) {
            logger.error("v3w4x5y6 | 查找匹配的元数据数据源过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * 根据元数据数据源ID查找匹配的血缘数据源
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 匹配的血缘数据源，如果没有匹配则返回404
     */
    @GetMapping("/metadata/{metadataDataSourceId}/matched-lineage")
    fun findMatchedLineageDatasource(
        @PathVariable metadataDataSourceId: Long
    ): ResponseEntity<LineageDatasource> {
        logger.info("z7a8b9c0 | 查找元数据数据源 $metadataDataSourceId 匹配的血缘数据源")
        
        return try {
            val lineageDatasource = datasourceMatchingService
                .findMatchedLineageDatasourceByMetadataId(metadataDataSourceId)
            
            if (lineageDatasource != null) {
                logger.info("d1e2f3g4 | 找到匹配的血缘数据源: ${lineageDatasource.id}")
                ResponseEntity.ok(lineageDatasource)
            } else {
                logger.info("h5i6j7k8 | 未找到元数据数据源 $metadataDataSourceId 匹配的血缘数据源")
                ResponseEntity.notFound().build()
            }
        } catch (e: Exception) {
            logger.error("l9m0n1o2 | 查找匹配的血缘数据源过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * 检查血缘数据源是否有匹配的元数据数据源
     *
     * @param lineageDatasourceId 血缘数据源ID
     * @return 匹配状态信息
     */
    @GetMapping("/lineage/{lineageDatasourceId}/has-match")
    fun checkLineageDatasourceHasMatch(
        @PathVariable lineageDatasourceId: Long
    ): ResponseEntity<MatchStatusResponse> {
        logger.info("p3q4r5s6 | 检查血缘数据源 $lineageDatasourceId 是否有匹配")
        
        return try {
            val hasMatch = datasourceMatchingService.hasMatchedMetadataDataSource(lineageDatasourceId)
            val response = MatchStatusResponse(
                datasourceId = lineageDatasourceId,
                datasourceType = "lineage",
                hasMatch = hasMatch
            )
            
            logger.info("t7u8v9w0 | 血缘数据源 $lineageDatasourceId 匹配状态: $hasMatch")
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error("x1y2z3a4 | 检查血缘数据源匹配状态过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
    
    /**
     * 检查元数据数据源是否有匹配的血缘数据源
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 匹配状态信息
     */
    @GetMapping("/metadata/{metadataDataSourceId}/has-match")
    fun checkMetadataDataSourceHasMatch(
        @PathVariable metadataDataSourceId: Long
    ): ResponseEntity<MatchStatusResponse> {
        logger.info("b5c6d7e8 | 检查元数据数据源 $metadataDataSourceId 是否有匹配")
        
        return try {
            val hasMatch = datasourceMatchingService.hasMatchedLineageDatasource(metadataDataSourceId)
            val response = MatchStatusResponse(
                datasourceId = metadataDataSourceId,
                datasourceType = "metadata",
                hasMatch = hasMatch
            )
            
            logger.info("f9g0h1i2 | 元数据数据源 $metadataDataSourceId 匹配状态: $hasMatch")
            ResponseEntity.ok(response)
        } catch (e: Exception) {
            logger.error("j3k4l5m6 | 检查元数据数据源匹配状态过程中发生错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
}

/**
 * 匹配状态响应 (Match Status Response)
 *
 * 用于返回数据源匹配状态的响应对象
 */
data class MatchStatusResponse(
    val datasourceId: Long,
    val datasourceType: String, // "lineage" 或 "metadata"
    val hasMatch: Boolean
)
